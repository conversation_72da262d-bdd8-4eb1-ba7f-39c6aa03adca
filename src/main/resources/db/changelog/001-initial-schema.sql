--liquibase formatted sql

--changeset phdhuy:001-initial-schema
--comment: Create initial database schema for Bullish Bot

-- Create users table
CREATE TABLE users
(
    id               UUID PRIMARY KEY,
    created_at       TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       TIMESTAMP(6),
    deleted_at       TIMESTAMP(6),
    telegram_chat_id BIGINT       NOT NULL,
    username         VA<PERSON>HA<PERSON>(255),
    first_name       VA<PERSON>HA<PERSON>(255),
    last_name        VA<PERSON>HAR(255)
);

-- Create unique index on telegram_chat_id
CREATE UNIQUE INDEX idx_users_telegram_chat_id ON users (telegram_chat_id);

-- Create tickers table
CREATE TABLE tickers
(
    id         UUID PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    symbol     VARCHAR(10)  NOT NULL,
    name       <PERSON><PERSON><PERSON><PERSON>(255),
    short_name <PERSON><PERSON><PERSON><PERSON>(100),
    floor      VARCHAR(100),
    type       VARCHAR(100) NOT NULL CHECK (type IN ('STOCK', 'CRYPTO'))
);

-- Create unique index on symbol
CREATE UNIQUE INDEX idx_tickers_symbol ON tickers (symbol);

-- Create subscriptions table
CREATE TABLE subscriptions
(
    id         UUID PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    user_id    UUID         NOT NULL,
    ticker_id  UUID         NOT NULL,
    CONSTRAINT uk_subscriptions_user_ticker UNIQUE (user_id, ticker_id),
    CONSTRAINT fk_subscriptions_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    CONSTRAINT fk_subscriptions_ticker_id FOREIGN KEY (ticker_id) REFERENCES tickers (id) ON DELETE CASCADE
);