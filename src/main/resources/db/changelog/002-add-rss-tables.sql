-- RSS Feeds table
CREATE TABLE rss_feeds
(
    id                     UUID PRIMARY KEY      DEFAULT gen_random_uuid(),
    name                   VARCHAR(255) NOT NULL,
    url                    VARCHAR(500) NOT NULL UNIQUE,
    description            TEXT,
    is_active              BOOLEAN      NOT NULL DEFAULT true,
    last_crawled_at        TIMESTAMP,
    crawl_interval_minutes INTEGER      NOT NULL DEFAULT 60,
    created_at             TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at             TIMESTAMP
);

-- News Articles table
CREATE TABLE news_articles
(
    id             UUID PRIMARY KEY      DEFAULT gen_random_uuid(),
    title          VARCHAR(500) NOT NULL,
    description    TEXT,
    content        TEXT,
    url            VARCHAR(500) NOT NULL UNIQUE,
    author         VA<PERSON><PERSON><PERSON>(255),
    published_date TIMESTAMP,
    guid           VARCHAR(500),
    rss_feed_id    UUID         NOT NULL,
    created_at     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at     TIMESTAMP,
    FOREIGN KEY (rss_feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
);

-- News Article Tickers table (for extracted ticker symbols)
CREATE TABLE news_article_tickers
(
    news_article_id UUID        NOT NULL,
    ticker_symbol   VARCHAR(10) NOT NULL,
    PRIMARY KEY (news_article_id, ticker_symbol),
    FOREIGN KEY (news_article_id) REFERENCES news_articles (id) ON DELETE CASCADE
);

-- News Notifications table
CREATE TABLE news_notifications
(
    id              UUID PRIMARY KEY     DEFAULT gen_random_uuid(),
    user_id         UUID        NOT NULL,
    news_article_id UUID        NOT NULL,
    status          VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    sent_at         TIMESTAMP,
    error_message   TEXT,
    retry_count     INTEGER     NOT NULL DEFAULT 0,
    created_at      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at      TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (news_article_id) REFERENCES news_articles (id) ON DELETE CASCADE,
    UNIQUE (user_id, news_article_id)
);
