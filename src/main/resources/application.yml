server:
  port: 8081
spring:
  profiles:
    active: dev
  application:
    name: bullish-bot

  datasource:
    url: ********************************/${POSTGRES_DB}
    username: ${POSTGRES_USER}
    password: ${POSTGRES_PASSWORD}
    hikari:
      max-lifetime: 60000
      auto-commit: false
  data:
    redis:
      host: ${SPRING_DATA_REDIS_HOST:redis}
      port: ${SPRING_DATA_REDIS_PORT:6379}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.xml
  thymeleaf:
    mode: HTML
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
springdoc:
  swagger-ui:
    path: /api/index.html
logging:
  pattern:
    correlation: "${spring.application.name:},%X{traceId:-},%X{spanId:-}"