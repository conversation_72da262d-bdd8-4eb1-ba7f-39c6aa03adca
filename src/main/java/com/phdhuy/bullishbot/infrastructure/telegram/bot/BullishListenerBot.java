package com.phdhuy.bullishbot.infrastructure.telegram.bot;

import com.phdhuy.bullishbot.application.service.TelegramCommandHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.longpolling.util.LongPollingSingleThreadUpdateConsumer;
import org.telegram.telegrambots.meta.api.objects.Update;

@Component
@RequiredArgsConstructor
@Slf4j
public class BullishListenerBot implements LongPollingSingleThreadUpdateConsumer {

  private final TelegramCommandHandler telegramCommandHandler;

  @Override
  public void consume(Update update) {
    if (update.hasMessage() && update.getMessage().hasText()) {
      Long chatId = update.getMessage().getChatId();
      String messageText = update.getMessage().getText();

      log.info("Received message from chat {}: {}", chatId, messageText);

      try {
        telegramCommandHandler.handleCommand(chatId, messageText);
      } catch (Exception e) {
        log.error("Error handling command from chat {}: {}", chatId, e.getMessage(), e);
      }
    }
  }
}
