package com.phdhuy.bullishbot.infrastructure.telegram.adapter;

import com.phdhuy.bullishbot.domain.port.out.TelegramService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.ReplyKeyboardMarkup;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.buttons.KeyboardButton;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.buttons.KeyboardRow;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.meta.generics.TelegramClient;

@Service
@RequiredArgsConstructor
@Slf4j
public class TelegramServiceAdapter implements TelegramService {

  private final TelegramClient telegramClient;

  @Override
  public void sendMessage(Long chatId, String message) {
    SendMessage sendMessage =
        SendMessage.builder().chatId(chatId).text(message).parseMode("MarkdownV2").build();

    try {
      telegramClient.execute(sendMessage);
      log.info("Sent message to chat {}: {}", chatId, message);
    } catch (TelegramApiException e) {
      log.error("Failed to send message to chat {}: {}", chatId, e.getMessage(), e);
    }
  }

  @Override
  public void sendMessageWithMenu(Long chatId, String message) {
    SendMessage sendMessage =
        SendMessage.builder()
            .chatId(chatId)
            .text(message)
            .replyMarkup(createMenuKeyboard())
            .parseMode("MarkdownV2")
            .build();

    try {
      telegramClient.execute(sendMessage);
      log.info("Sent message with menu to chat {}: {}", chatId, message);
    } catch (TelegramApiException e) {
      log.error("Failed to send message with menu to chat {}: {}", chatId, e.getMessage(), e);
    }
  }

  private ReplyKeyboardMarkup createMenuKeyboard() {
    List<KeyboardRow> keyboard = new ArrayList<>();

    // First row
    KeyboardRow row1 = new KeyboardRow();
    row1.add(new KeyboardButton("/start"));
    row1.add(new KeyboardButton("/help"));
    keyboard.add(row1);

    // Second row
    KeyboardRow row2 = new KeyboardRow();
    row2.add(new KeyboardButton("/mytickers"));
    keyboard.add(row2);

    return ReplyKeyboardMarkup.builder()
        .keyboard(keyboard)
        .resizeKeyboard(true)
        .oneTimeKeyboard(false)
        .build();
  }
}
