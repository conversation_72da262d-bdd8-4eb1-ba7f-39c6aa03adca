package com.phdhuy.bullishbot.infrastructure.telegram.config;

import com.phdhuy.bullishbot.infrastructure.telegram.bot.BullishListenerBot;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.telegram.telegrambots.longpolling.TelegramBotsLongPollingApplication;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class TelegramBotConsumerConfig {

  @Value("${telegram.bot.token}")
  private String botToken;

  private final BullishListenerBot bullishListenerBot;

  private TelegramBotsLongPollingApplication botsApplication;

  @PostConstruct
  public void configTelegramBotConsumer() throws TelegramApiException {
    if (botToken != null && !botToken.trim().isEmpty() && !botToken.equals("your_bot_token_here")) {
      botsApplication = new TelegramBotsLongPollingApplication();
      botsApplication.registerBot(botToken, bullishListenerBot);
      log.info("Telegram bot initialized successfully with token");
    } else {
      log.warn(
          "Telegram bot token not provided. Bot will not be initialized. Set TELEGRAM_BOT_TOKEN"
              + " environment variable.");
    }
  }
}
