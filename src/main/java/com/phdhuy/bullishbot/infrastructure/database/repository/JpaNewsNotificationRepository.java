package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import com.phdhuy.bullishbot.domain.port.out.NewsNotificationRepository;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaNewsNotificationRepository
    extends JpaRepository<NewsNotification, UUID>, NewsNotificationRepository {
  List<NewsNotification> findByUser(User user);

  List<NewsNotification> findByNewsArticle(NewsArticle newsArticle);

  List<NewsNotification> findByStatus(NotificationStatus status);

  List<NewsNotification> findByStatusAndRetryCountLessThan(
      NotificationStatus status, Integer maxRetries);

  boolean existsByUserAndNewsArticle(User user, NewsArticle newsArticle);
}
