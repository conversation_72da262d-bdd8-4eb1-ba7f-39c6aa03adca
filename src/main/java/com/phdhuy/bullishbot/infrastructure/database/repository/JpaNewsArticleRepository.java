package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.RssFeed;
import com.phdhuy.bullishbot.domain.port.out.NewsArticleRepository;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaNewsArticleRepository
    extends JpaRepository<NewsArticle, UUID>, NewsArticleRepository {
  Optional<NewsArticle> findByUrl(String url);

  Optional<NewsArticle> findByGuid(String guid);

  List<NewsArticle> findByRssFeed(RssFeed rssFeed);

  List<NewsArticle> findByRssFeedAndPublishedDateAfter(RssFeed rssFeed, Instant after);

  @Query("SELECT n FROM NewsArticle n JOIN n.extractedTickers t WHERE t = :ticker")
  List<NewsArticle> findByExtractedTickersContaining(@Param("ticker") String ticker);

  boolean existsByUrl(String url);

  boolean existsByGuid(String guid);
}
