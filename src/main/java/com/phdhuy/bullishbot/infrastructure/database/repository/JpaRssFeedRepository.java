package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.RssFeed;
import com.phdhuy.bullishbot.domain.port.out.RssFeedRepository;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaRssFeedRepository extends JpaRepository<RssFeed, UUID>, RssFeedRepository {
  Optional<RssFeed> findByUrl(String url);

  List<RssFeed> findByIsActiveTrue();

  @Query(
      "SELECT r FROM RssFeed r WHERE r.isActive = true AND (r.lastCrawledAt IS NULL OR"
          + " r.lastCrawledAt < :cutoffTime)")
  List<RssFeed> findByIsActiveTrueAndLastCrawledAtBefore(@Param("cutoffTime") Instant cutoffTime);

  boolean existsByUrl(String url);
}
