package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.port.out.UserRepository;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaUserRepository extends JpaRepository<User, UUID>, UserRepository {

  @Override
  Optional<User> findByTelegramChatId(Long telegramChatId);
}
