package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.Ticker;
import com.phdhuy.bullishbot.domain.port.out.TickerRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaTickerRepository extends JpaRepository<Ticker, UUID>, TickerRepository {

  @Override
  Optional<Ticker> findBySymbol(String symbol);

  @Query("SELECT DISTINCT t.symbol FROM Ticker t")
  List<String> findAllSymbols();
}
