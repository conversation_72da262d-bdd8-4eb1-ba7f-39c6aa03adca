package com.phdhuy.bullishbot.domain.entity;

import com.phdhuy.bullishbot.common.entity.BaseEntity;
import jakarta.persistence.*;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "users")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class User extends BaseEntity {

  @Id @GeneratedValue private UUID id;

  @Column(name = "telegram_chat_id", unique = true, nullable = false)
  private Long telegramChatId;

  @Column(name = "username")
  private String username;

  @Column(name = "first_name")
  private String firstName;

  @Column(name = "last_name")
  private String lastName;

  @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<Subscription> subscriptions;
}
