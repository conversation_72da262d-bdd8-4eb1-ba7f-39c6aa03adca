package com.phdhuy.bullishbot.domain.entity;

import com.phdhuy.bullishbot.common.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "news_articles")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class NewsArticle extends BaseEntity {

  @Id @GeneratedValue private UUID id;

  @Column(name = "title", nullable = false)
  @NotBlank
  private String title;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;

  @Column(name = "content", columnDefinition = "TEXT")
  private String content;

  @Column(name = "url", nullable = false, unique = true)
  @NotBlank
  private String url;

  @Column(name = "author")
  private String author;

  @Column(name = "published_date")
  private Instant publishedDate;

  @Column(name = "guid")
  private String guid;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "rss_feed_id", nullable = false)
  @NotNull
  private RssFeed rssFeed;

  @OneToMany(mappedBy = "newsArticle", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<NewsNotification> notifications;

  @ElementCollection
  @CollectionTable(
      name = "news_article_tickers",
      joinColumns = @JoinColumn(name = "news_article_id"))
  @Column(name = "ticker_symbol")
  private Set<String> extractedTickers;
}
