package com.phdhuy.bullishbot.domain.entity;

import com.phdhuy.bullishbot.common.entity.BaseEntity;
import com.phdhuy.bullishbot.domain.entity.enums.TickerType;
import jakarta.persistence.*;
import jakarta.persistence.Column;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tickers")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Ticker extends BaseEntity {

  @Id
  @Column(name = "id")
  private UUID id;

  @Column(name = "symbol", nullable = false, length = 10)
  private String symbol;

  @Column(name = "name", length = 255)
  private String name;

  @Column(name = "short_name", length = 100)
  private String shortName;

  @Column(name = "floor", length = 100)
  private String floor;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false, length = 100)
  private TickerType type;

  @OneToMany(mappedBy = "ticker", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<Subscription> subscriptions;
}
