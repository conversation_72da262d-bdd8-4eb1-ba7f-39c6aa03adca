package com.phdhuy.bullishbot.domain.entity;

import com.phdhuy.bullishbot.common.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "rss_feeds")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class RssFeed extends BaseEntity {

  @Id @GeneratedValue private UUID id;

  @Column(name = "name", nullable = false)
  @NotBlank
  private String name;

  @Column(name = "url", nullable = false, unique = true)
  @NotBlank
  private String url;

  @Column(name = "description")
  private String description;

  @Column(name = "is_active", nullable = false)
  @NotNull
  @Builder.Default
  private Boolean isActive = true;

  @Column(name = "last_crawled_at")
  private Instant lastCrawledAt;

  @Column(name = "crawl_interval_minutes", nullable = false)
  @NotNull
  @Builder.Default
  private Integer crawlIntervalMinutes = 60;

  @OneToMany(mappedBy = "rssFeed", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<NewsArticle> newsArticles;
}
