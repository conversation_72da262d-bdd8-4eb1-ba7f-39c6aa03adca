package com.phdhuy.bullishbot.domain.entity;

import com.phdhuy.bullishbot.common.entity.BaseEntity;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(
    name = "news_notifications",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"user_id", "news_article_id"})})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class NewsNotification extends BaseEntity {

  @Id @GeneratedValue private UUID id;

  @ManyToOne
  @JoinColumn(name = "user_id", nullable = false)
  @NotNull
  private User user;

  @ManyToOne
  @JoinColumn(name = "news_article_id", nullable = false)
  @NotNull
  private NewsArticle newsArticle;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  @NotNull
  @Builder.Default
  private NotificationStatus status = NotificationStatus.PENDING;

  @Column(name = "sent_at")
  private Instant sentAt;

  @Column(name = "error_message")
  private String errorMessage;

  @Column(name = "retry_count", nullable = false)
  @Builder.Default
  private Integer retryCount = 0;
}
