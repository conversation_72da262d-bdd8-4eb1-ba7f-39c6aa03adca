package com.phdhuy.bullishbot.domain.port.in;

import java.util.List;

public interface SubscribeToTickerUseCase {

  /**
   * Subscribe a user to a ticker symbol
   *
   * @param telegramChatId the user's telegram chat ID
   * @param symbol the ticker symbol to subscribe to
   */
  void subscribeUserToTicker(Long telegramChatId, String symbol);

  /**
   * Unsubscribe a user from a ticker symbol
   *
   * @param telegramChatId the user's telegram chat ID
   * @param symbol the ticker symbol to unsubscribe from
   */
  void unsubscribeUserFromTicker(Long telegramChatId, String symbol);

  /**
   * Get all ticker symbols a user is subscribed to
   *
   * @param telegramChatId the user's telegram chat ID
   * @return list of ticker symbols
   */
  List<String> getUserSubscriptions(Long telegramChatId);

  /**
   * Check if a user is subscribed to a specific ticker
   *
   * @param telegramChatId the user's telegram chat ID
   * @param symbol the ticker symbol to check
   * @return true if subscribed, false otherwise
   */
  boolean isUserSubscribedToTicker(Long telegramChatId, String symbol);
}
