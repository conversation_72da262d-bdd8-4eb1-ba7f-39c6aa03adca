package com.phdhuy.bullishbot.domain.port.out;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface NewsNotificationRepository {

  NewsNotification save(NewsNotification newsNotification);

  Optional<NewsNotification> findById(UUID id);

  List<NewsNotification> findByUser(User user);

  List<NewsNotification> findByNewsArticle(NewsArticle newsArticle);

  List<NewsNotification> findByStatus(NotificationStatus status);

  List<NewsNotification> findByStatusAndRetryCountLessThan(
      NotificationStatus status, Integer maxRetries);

  boolean existsByUserAndNewsArticle(User user, NewsArticle newsArticle);

  void delete(NewsNotification newsNotification);
}
