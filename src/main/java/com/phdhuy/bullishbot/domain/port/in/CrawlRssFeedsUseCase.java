package com.phdhuy.bullishbot.domain.port.in;

import com.phdhuy.bullishbot.domain.entity.RssFeed;
import java.util.concurrent.CompletableFuture;

public interface CrawlRssFeedsUseCase {

  /**
   * Crawl all active RSS feeds
   *
   * @return CompletableFuture that completes when all feeds are crawled
   */
  CompletableFuture<Void> crawlAllActiveFeeds();

  /**
   * Crawl a specific RSS feed
   *
   * @param rssFeed the RSS feed to crawl
   * @return CompletableFuture that completes when the feed is crawled
   */
  CompletableFuture<Void> crawlFeed(RssFeed rssFeed);

  /**
   * Validate if an RSS feed URL is accessible and valid
   *
   * @param url the RSS feed URL to validate
   * @return true if valid, false otherwise
   */
  boolean validateFeedUrl(String url);
}
