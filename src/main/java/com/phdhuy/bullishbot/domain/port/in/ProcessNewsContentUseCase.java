package com.phdhuy.bullishbot.domain.port.in;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import java.util.Set;

public interface ProcessNewsContentUseCase {

  /**
   * Process news article content and extract ticker symbols
   *
   * @param newsArticle the news article to process
   */
  void processNewsContent(NewsArticle newsArticle);

  /**
   * Extract ticker symbols from news article content
   *
   * @param newsArticle the news article to process
   * @return set of ticker symbols found in the content
   */
  Set<String> extractTickerSymbols(NewsArticle newsArticle);

  /**
   * Clean and normalize text content for processing
   *
   * @param content the raw content to clean
   * @return cleaned content
   */
  String cleanContent(String content);
}
