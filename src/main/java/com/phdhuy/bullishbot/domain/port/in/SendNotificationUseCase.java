package com.phdhuy.bullishbot.domain.port.in;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import java.util.concurrent.CompletableFuture;

public interface SendNotificationUseCase {

  /**
   * Process news article and send notifications to subscribed users
   *
   * @param newsArticle the news article to process for notifications
   * @return CompletableFuture that completes when all notifications are processed
   */
  CompletableFuture<Void> processNewsForNotifications(NewsArticle newsArticle);

  /**
   * Send a notification to a specific user
   *
   * @param telegramChatId the user's telegram chat ID
   * @param newsArticle the news article to notify about
   * @param matchedTickers the ticker symbols that matched the user's subscriptions
   * @return CompletableFuture that completes when the notification is sent
   */
  CompletableFuture<Void> sendNotification(
      Long telegramChatId, NewsArticle newsArticle, String matchedTickers);

  /**
   * Retry failed notifications
   *
   * @return CompletableFuture that completes when retry process is finished
   */
  CompletableFuture<Void> retryFailedNotifications();
}
