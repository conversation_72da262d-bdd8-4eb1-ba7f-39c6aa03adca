package com.phdhuy.bullishbot.domain.port.out;

import com.phdhuy.bullishbot.domain.entity.RssFeed;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RssFeedRepository {

  RssFeed save(RssFeed rssFeed);

  Optional<RssFeed> findById(UUID id);

  Optional<RssFeed> findByUrl(String url);

  List<RssFeed> findByIsActiveTrue();

  List<RssFeed> findByIsActiveTrueAndLastCrawledAtBefore(Instant cutoffTime);

  void delete(RssFeed rssFeed);

  boolean existsByUrl(String url);
}
