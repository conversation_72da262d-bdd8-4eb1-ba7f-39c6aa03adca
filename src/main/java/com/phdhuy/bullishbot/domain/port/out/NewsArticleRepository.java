package com.phdhuy.bullishbot.domain.port.out;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.RssFeed;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface NewsArticleRepository {

  NewsArticle save(NewsArticle newsArticle);

  Optional<NewsArticle> findById(UUID id);

  Optional<NewsArticle> findByUrl(String url);

  Optional<NewsArticle> findByGuid(String guid);

  List<NewsArticle> findByRssFeed(RssFeed rssFeed);

  List<NewsArticle> findByRssFeedAndPublishedDateAfter(RssFeed rssFeed, Instant after);

  List<NewsArticle> findByExtractedTickersContaining(String ticker);

  boolean existsByUrl(String url);

  boolean existsByGuid(String guid);

  void delete(NewsArticle newsArticle);
}
