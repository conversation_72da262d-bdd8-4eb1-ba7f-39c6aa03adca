package com.phdhuy.bullishbot.domain.service;

import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.springframework.stereotype.Service;

/**
 * Service responsible for processing HTML content and extracting clean text. Handles CDATA
 * sections, HTML tag removal, and text normalization.
 */
@Service
@Slf4j
public class HtmlProcessingService {

  private static final String CDATA_START = "<![CDATA[";
  private static final String CDATA_END = "]]>";
  private static final int CDATA_START_LENGTH = 9; // Length of "<![CDATA["
  private static final int CDATA_END_LENGTH = 3; // Length of "]]>"

  /**
   * Cleans HTML content by removing tags and extracting meaningful text. Handles CDATA sections and
   * maintains proper text flow.
   *
   * @param html the HTML content to clean
   * @return cleaned text content
   */
  public String cleanHtml(String html) {
    if (isNullOrEmpty(html)) {
      return "";
    }

    String processedHtml = processCDataSections(html);
    Document doc = Jsoup.parse(processedHtml);

    log.debug("HTML content before processing: {}", processedHtml);

    StringBuilder result = new StringBuilder();
    processElement(doc.body(), result);

    String cleanedText = normalizeText(result.toString());

    log.debug("HTML content after processing: {}", cleanedText);
    return cleanedText;
  }

  /**
   * Simple HTML cleaning that only removes tags. Used for basic content cleaning without complex
   * processing.
   *
   * @param content the content to clean
   * @return content with HTML tags removed
   */
  public String cleanContent(String content) {
    if (isNullOrEmpty(content)) {
      return "";
    }

    return content
        .replaceAll("<[^>]+>", " ")
        .replaceAll("\\s+", " ")
        .replaceAll("[^a-zA-Z0-9\\s.,!?;:()\\[\\]{}\"'-]", " ")
        .trim();
  }

  /**
   * Processes CDATA sections by extracting their content.
   *
   * @param html the HTML content that may contain CDATA sections
   * @return HTML content with CDATA sections processed
   */
  private String processCDataSections(String html) {
    if (isNullOrEmpty(html)) {
      return "";
    }

    String processed = html;

    if (containsCDataSection(processed)) {
      int cdataStart = processed.indexOf(CDATA_START);
      int cdataEnd = processed.indexOf(CDATA_END, cdataStart);

      if (isValidCDataSection(cdataStart, cdataEnd)) {
        String cdataContent = extractCDataContent(processed, cdataStart, cdataEnd);
        processed = replaceCDataSection(processed, cdataStart, cdataEnd, cdataContent);
      }
    }

    return processed;
  }

  /**
   * Recursively processes HTML elements to extract text content.
   *
   * @param element the HTML element to process
   * @param result the StringBuilder to append results to
   */
  private void processElement(Element element, StringBuilder result) {
    for (Node node : element.childNodes()) {
      if (node instanceof TextNode textNode) {
        processTextNode(textNode, result);
      } else if (node instanceof Element childElement) {
        processHtmlElement(childElement, result);
      }
    }
  }

  /**
   * Processes a text node by extracting and appending its text.
   *
   * @param textNode the text node to process
   * @param result the StringBuilder to append results to
   */
  private void processTextNode(TextNode textNode, StringBuilder result) {
    String text = textNode.text().trim();
    if (!text.isEmpty()) {
      result.append(text).append(" ");
    }
  }

  /**
   * Processes HTML elements based on their tag type.
   *
   * @param element the HTML element to process
   * @param result the StringBuilder to append results to
   */
  private void processHtmlElement(Element element, StringBuilder result) {
    String tagName = element.tagName().toLowerCase();

    switch (tagName) {
      case "img" -> processImageElement(element, result);
      case "a" -> processLinkElement(element, result);
      case "br" -> result.append(" ");
      case "p", "div", "h1", "h2", "h3", "h4", "h5", "h6" -> processBlockElement(element, result);
      case "script", "style", "noscript" -> {
        /* Skip these elements */
      }
      default -> processElement(element, result);
    }
  }

  /**
   * Processes image elements by extracting alt or title text.
   *
   * @param img the image element
   * @param result the StringBuilder to append results to
   */
  private void processImageElement(Element img, StringBuilder result) {
    String imgText = extractImageText(img);
    if (!imgText.isEmpty()) {
      result.append(imgText).append(" ");
    }
  }

  /**
   * Processes link elements by extracting both child content and link text.
   *
   * @param link the link element
   * @param result the StringBuilder to append results to
   */
  private void processLinkElement(Element link, StringBuilder result) {
    // Process children first (to get image alt text)
    processElement(link, result);

    // Then add direct link text
    String linkText = link.ownText().trim();
    if (!linkText.isEmpty()) {
      result.append(linkText).append(" ");
    }
  }

  /**
   * Processes block elements by adding spacing and processing children.
   *
   * @param element the block element
   * @param result the StringBuilder to append results to
   */
  private void processBlockElement(Element element, StringBuilder result) {
    result.append(" ");
    processElement(element, result);
    result.append(" ");
  }

  /**
   * Extracts meaningful text from image elements. Priority: alt attribute > title attribute > empty
   * string
   *
   * @param img the image element
   * @return the extracted text or empty string
   */
  private String extractImageText(Element img) {
    String alt = img.attr("alt");
    if (!alt.isEmpty()) {
      return alt;
    }

    String title = img.attr("title");
    if (!title.isEmpty()) {
      return title;
    }

    return "";
  }

  /**
   * Normalizes text by fixing spacing and punctuation.
   *
   * @param text the text to normalize
   * @return normalized text
   */
  private String normalizeText(String text) {
    return text.replaceAll("\\s+", " ") // Multiple whitespaces to single space
        .replaceAll("\\s*\\.\\s*", ". ") // Fix spacing around periods
        .replaceAll("\\s*,\\s*", ", ") // Fix spacing around commas
        .replaceAll("\\s*:\\s*", ": ") // Fix spacing around colons
        .replaceAll("\\s*;\\s*", "; ") // Fix spacing around semicolons
        .replaceAll("\\. \\. \\.", "...") // Fix ellipsis formatting
        .trim();
  }

  // Helper methods for CDATA processing
  private boolean isNullOrEmpty(String str) {
    return str == null || str.trim().isEmpty();
  }

  private boolean containsCDataSection(String html) {
    return html.contains(CDATA_START) && html.contains(CDATA_END);
  }

  private boolean isValidCDataSection(int cdataStart, int cdataEnd) {
    return cdataStart != -1 && cdataEnd != -1;
  }

  private String extractCDataContent(String html, int cdataStart, int cdataEnd) {
    return html.substring(cdataStart + CDATA_START_LENGTH, cdataEnd);
  }

  private String replaceCDataSection(String html, int cdataStart, int cdataEnd, String content) {
    return html.substring(0, cdataStart) + content + html.substring(cdataEnd + CDATA_END_LENGTH);
  }
}
