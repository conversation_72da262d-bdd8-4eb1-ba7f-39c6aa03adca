package com.phdhuy.bullishbot.domain.service;

import com.rometools.rome.feed.synd.SyndFeed;
import com.rometools.rome.io.SyndFeedInput;
import com.rometools.rome.io.XmlReader;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Service;

/**
 * Service responsible for fetching RSS feeds from remote URLs. Handles HTTP requests, response
 * validation, and RSS parsing.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RssFeedFetcherService {

  private final OkHttpClient httpClient;

  private static final String USER_AGENT = "BullishBot/1.0";
  private static final String USER_AGENT_HEADER = "User-Agent";

  /**
   * Fetches and parses an RSS feed from the given URL.
   *
   * @param url the RSS feed URL
   * @return Optional containing the parsed SyndFeed, or empty if fetch failed
   */
  public Optional<SyndFeed> fetchFeed(String url) {
    try {
      log.debug("Fetching RSS feed from URL: {}", url);

      Request request = buildRequest(url);

      try (Response response = httpClient.newCall(request).execute()) {
        return processFeedResponse(url, response);
      }

    } catch (Exception e) {
      log.error("Error fetching RSS feed from URL: {}", url, e);
      return Optional.empty();
    }
  }

  /**
   * Validates if a URL contains a valid RSS feed.
   *
   * @param url the URL to validate
   * @return true if the URL contains a valid RSS feed, false otherwise
   */
  public boolean isValidFeedUrl(String url) {
    try {
      Optional<SyndFeed> syndFeed = fetchFeed(url);
      return syndFeed.isPresent() && syndFeed.get().getTitle() != null;
    } catch (Exception e) {
      log.warn("RSS feed validation failed for URL: {}", url, e);
      return false;
    }
  }

  /**
   * Builds an HTTP request for the RSS feed URL.
   *
   * @param url the RSS feed URL
   * @return configured Request object
   */
  private Request buildRequest(String url) {
    return new Request.Builder().url(url).header(USER_AGENT_HEADER, USER_AGENT).build();
  }

  /**
   * Processes the HTTP response and parses the RSS feed.
   *
   * @param url the original URL (for logging)
   * @param response the HTTP response
   * @return Optional containing the parsed SyndFeed, or empty if processing failed
   */
  private Optional<SyndFeed> processFeedResponse(String url, Response response) {
    if (!response.isSuccessful()) {
      log.warn("HTTP request failed for RSS feed: {} - Status: {}", url, response.code());
      return Optional.empty();
    }

    if (response.body() == null) {
      log.warn("Empty response body for RSS feed: {}", url);
      return Optional.empty();
    }

    return parseFeedContent(url, response);
  }

  /**
   * Parses the RSS feed content from the HTTP response.
   *
   * @param url the original URL (for logging)
   * @param response the HTTP response containing RSS content
   * @return Optional containing the parsed SyndFeed, or empty if parsing failed
   */
  private Optional<SyndFeed> parseFeedContent(String url, Response response) {
    try {
      SyndFeedInput input = new SyndFeedInput();
      SyndFeed syndFeed = input.build(new XmlReader(response.body().byteStream()));

      log.debug("Successfully parsed RSS feed from URL: {}", url);
      return Optional.of(syndFeed);

    } catch (Exception e) {
      log.error("Error parsing RSS feed content from URL: {}", url, e);
      return Optional.empty();
    }
  }
}
