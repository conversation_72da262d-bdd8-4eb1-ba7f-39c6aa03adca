package com.phdhuy.bullishbot.domain.valueobject;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import lombok.Value;

/**
 * Value object representing a validated RSS feed URL. Ensures URL validity and provides type
 * safety.
 */
@Value
public class FeedUrl {

  String value;

  private FeedUrl(String value) {
    this.value = Objects.requireNonNull(value, "Feed URL cannot be null");
  }

  /**
   * Creates a FeedUrl from a string, validating the URL format.
   *
   * @param url the URL string
   * @return FeedUrl instance
   * @throws IllegalArgumentException if the URL is invalid
   */
  public static FeedUrl of(String url) {
    if (url == null || url.trim().isEmpty()) {
      throw new IllegalArgumentException("Feed URL cannot be null or empty");
    }

    String trimmedUrl = url.trim();

    try {
      URI uri = new URI(trimmedUrl);
      if (uri.getScheme() == null || uri.getHost() == null) {
        throw new IllegalArgumentException("Invalid URL format: " + trimmedUrl);
      }
      return new FeedUrl(trimmedUrl);
    } catch (URISyntaxException e) {
      throw new IllegalArgumentException("Invalid URL format: " + trimmedUrl, e);
    }
  }

  /**
   * Creates a FeedUrl without validation (for trusted sources).
   *
   * @param url the URL string
   * @return FeedUrl instance
   */
  public static FeedUrl ofTrusted(String url) {
    return new FeedUrl(url);
  }

  @Override
  public String toString() {
    return value;
  }
}
