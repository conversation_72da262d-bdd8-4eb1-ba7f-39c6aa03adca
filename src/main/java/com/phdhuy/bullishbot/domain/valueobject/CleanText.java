package com.phdhuy.bullishbot.domain.valueobject;

import lombok.Value;

/**
 * Value object representing cleaned, processed text content. Ensures text has been properly cleaned
 * and normalized.
 */
@Value
public class CleanText {

  String value;

  private CleanText(String value) {
    this.value = value != null ? value.trim() : "";
  }

  /**
   * Creates CleanText from a string.
   *
   * @param text the text content
   * @return CleanText instance
   */
  public static CleanText of(String text) {
    return new CleanText(text);
  }

  /**
   * Creates empty CleanText.
   *
   * @return empty CleanText instance
   */
  public static CleanText empty() {
    return new CleanText("");
  }

  /**
   * Checks if the text content is empty.
   *
   * @return true if empty, false otherwise
   */
  public boolean isEmpty() {
    return value.isEmpty();
  }

  /**
   * Checks if the text content is not empty.
   *
   * @return true if not empty, false otherwise
   */
  public boolean isNotEmpty() {
    return !isEmpty();
  }

  /**
   * Gets the length of the text content.
   *
   * @return content length
   */
  public int length() {
    return value.length();
  }

  /**
   * Converts to uppercase.
   *
   * @return CleanText in uppercase
   */
  public CleanText toUpperCase() {
    return new CleanText(value.toUpperCase());
  }

  /**
   * Converts to lowercase.
   *
   * @return CleanText in lowercase
   */
  public CleanText toLowerCase() {
    return new CleanText(value.toLowerCase());
  }

  @Override
  public String toString() {
    return value;
  }
}
