package com.phdhuy.bullishbot.domain.valueobject;

import lombok.Value;

/**
 * Value object representing HTML content. Provides type safety and encapsulates HTML-specific
 * operations.
 */
@Value
public class HtmlContent {

  String value;

  private HtmlContent(String value) {
    this.value = value != null ? value : "";
  }

  /**
   * Creates HtmlContent from a string.
   *
   * @param html the HTML content string
   * @return HtmlContent instance
   */
  public static HtmlContent of(String html) {
    return new HtmlContent(html);
  }

  /**
   * Creates empty HtmlContent.
   *
   * @return empty HtmlContent instance
   */
  public static HtmlContent empty() {
    return new HtmlContent("");
  }

  /**
   * Checks if the HTML content is empty or null.
   *
   * @return true if empty, false otherwise
   */
  public boolean isEmpty() {
    return value == null || value.trim().isEmpty();
  }

  /**
   * Checks if the HTML content is not empty.
   *
   * @return true if not empty, false otherwise
   */
  public boolean isNotEmpty() {
    return !isEmpty();
  }

  /**
   * Gets the length of the HTML content.
   *
   * @return content length
   */
  public int length() {
    return value != null ? value.length() : 0;
  }

  @Override
  public String toString() {
    return value;
  }
}
