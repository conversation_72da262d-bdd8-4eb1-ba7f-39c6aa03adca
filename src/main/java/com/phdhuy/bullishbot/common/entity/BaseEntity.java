package com.phdhuy.bullishbot.common.entity;

import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@SQLRestriction(value = "deleted_at is null")
public class BaseEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @CreatedDate private Instant createdAt;

  @LastModifiedDate private Instant updatedAt;

  private Instant deletedAt;
}
