package com.phdhuy.bullishbot.common.constants;

public final class RssProcessingConstants {

  private RssProcessingConstants() {}

  public static final String TICKER_PATTERN_REGEX =
      "\\$([A-Z]{1,5})\\b|\\(([A-Z]{1,5})\\)|\\b([A-Z]{1,5})\\b|\\b([A-Z]{1,5}):";
  public static final int TICKER_PATTERN_GROUPS = 4;

  // Logging Messages
  public static final String LOG_CRAWLING_FEED = "Crawling RSS feed: {} ({})";
  public static final String LOG_FEED_SUCCESS = "Successfully crawled RSS feed: {}";
  public static final String LOG_FEED_FAILED = "Failed to fetch RSS feed: {}";
  public static final String LOG_HTTP_FAILED = "HTTP request failed for RSS feed: {} - Status: {}";
  public static final String LOG_EMPTY_RESPONSE = "Empty response body for RSS feed: {}";
  public static final String LOG_VALIDATION_FAILED = "RSS feed validation failed for URL: {}";
  public static final String LOG_ENTRY_NO_URL = "RSS entry has no URL, skipping: {}";
  public static final String LOG_PROCESSING_ENTRY_ERROR = "Error processing RSS entry: {}";
  public static final String LOG_CREATING_ARTICLE_ERROR =
      "Error creating news article from RSS entry: {}";
}
