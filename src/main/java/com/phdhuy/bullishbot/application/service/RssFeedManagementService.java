package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.domain.entity.RssFeed;
import com.phdhuy.bullishbot.domain.port.in.CrawlRssFeedsUseCase;
import com.phdhuy.bullishbot.domain.port.out.RssFeedRepository;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class RssFeedManagementService {

  private final RssFeedRepository rssFeedRepository;
  private final CrawlRssFeedsUseCase crawlRssFeedsUseCase;

  @Transactional
  public RssFeed addRssFeed(
      String name, String url, String description, Integer crawlIntervalMinutes) {
    log.info("Adding new RSS feed: {} ({})", name, url);

    // Check if feed already exists
    if (rssFeedRepository.existsByUrl(url)) {
      throw new IllegalArgumentException("RSS feed with URL already exists: " + url);
    }

    // Validate the feed URL
    if (!crawlRssFeedsUseCase.validateFeedUrl(url)) {
      throw new IllegalArgumentException("Invalid or inaccessible RSS feed URL: " + url);
    }

    RssFeed rssFeed =
        RssFeed.builder()
            .name(name)
            .url(url)
            .description(description)
            .crawlIntervalMinutes(crawlIntervalMinutes != null ? crawlIntervalMinutes : 60)
            .isActive(true)
            .build();

    rssFeed = rssFeedRepository.save(rssFeed);
    log.info("Successfully added RSS feed: {} with ID: {}", name, rssFeed.getId());

    return rssFeed;
  }

  @Transactional
  public void activateRssFeed(UUID feedId) {
    log.info("Activating RSS feed with ID: {}", feedId);

    RssFeed rssFeed =
        rssFeedRepository
            .findById(feedId)
            .orElseThrow(
                () -> new IllegalArgumentException("RSS feed not found with ID: " + feedId));

    rssFeed.setIsActive(true);
    rssFeedRepository.save(rssFeed);

    log.info("Successfully activated RSS feed: {}", rssFeed.getName());
  }

  @Transactional
  public void deactivateRssFeed(UUID feedId) {
    log.info("Deactivating RSS feed with ID: {}", feedId);

    RssFeed rssFeed =
        rssFeedRepository
            .findById(feedId)
            .orElseThrow(
                () -> new IllegalArgumentException("RSS feed not found with ID: " + feedId));

    rssFeed.setIsActive(false);
    rssFeedRepository.save(rssFeed);

    log.info("Successfully deactivated RSS feed: {}", rssFeed.getName());
  }

  @Transactional
  public void updateCrawlInterval(UUID feedId, Integer crawlIntervalMinutes) {
    log.info(
        "Updating crawl interval for RSS feed with ID: {} to {} minutes",
        feedId,
        crawlIntervalMinutes);

    RssFeed rssFeed =
        rssFeedRepository
            .findById(feedId)
            .orElseThrow(
                () -> new IllegalArgumentException("RSS feed not found with ID: " + feedId));

    rssFeed.setCrawlIntervalMinutes(crawlIntervalMinutes);
    rssFeedRepository.save(rssFeed);

    log.info("Successfully updated crawl interval for RSS feed: {}", rssFeed.getName());
  }

  public List<RssFeed> getAllActiveFeeds() {
    return rssFeedRepository.findByIsActiveTrue();
  }

  public Optional<RssFeed> getFeedById(UUID feedId) {
    return rssFeedRepository.findById(feedId);
  }

  public Optional<RssFeed> getFeedByUrl(String url) {
    return rssFeedRepository.findByUrl(url);
  }

  public List<RssFeed> getFeedsReadyForCrawling() {
    // Find feeds that haven't been crawled or are due for crawling based on their
    // interval
    Instant cutoffTime = Instant.now().minusSeconds(60 * 60); // Default 1 hour ago
    return rssFeedRepository.findByIsActiveTrueAndLastCrawledAtBefore(cutoffTime);
  }

  @Transactional
  public void deleteRssFeed(UUID feedId) {
    log.info("Deleting RSS feed with ID: {}", feedId);

    RssFeed rssFeed =
        rssFeedRepository
            .findById(feedId)
            .orElseThrow(
                () -> new IllegalArgumentException("RSS feed not found with ID: " + feedId));

    rssFeedRepository.delete(rssFeed);

    log.info("Successfully deleted RSS feed: {}", rssFeed.getName());
  }

  /** Initialize default RSS feeds if none exist */
  @Transactional
  public void initializeDefaultFeeds() {
    List<RssFeed> existingFeeds = rssFeedRepository.findByIsActiveTrue();

    if (existingFeeds.isEmpty()) {
      log.info("No RSS feeds found, initializing default feeds");

      addDefaultFeed(
          "VnExpress Kinh doanh",
          "https://vnexpress.net/rss/kinh-doanh.rss",
          "VnExpress finance & business news (Vietnam)",
          30);

      addDefaultFeed(
          "Bnews Kinh tế Việt Nam",
          "https://bnews.vn/rss/kinh-te-viet-nam-1.rss",
          "Economic news from Vietnam (Bnews)",
          30);

      addDefaultFeed(
          "Bnews Tài chính & Ngân hàng",
          "https://bnews.vn/rss/tai-chinh-&-ngan-hang-3.rss",
          "Finance & Banking news Vietnam",
          30);

      addDefaultFeed(
          "Bnews Chứng khoán",
          "https://bnews.vn/rss/chung-khoan-33.rss",
          "Vietnam Stock Market news",
          30);

      addDefaultFeed(
          "Nguoi quan sat Chứng khoán",
          "https://nguoiquansat.vn/rss/chung-khoan",
          "Vietnam Stock Market news",
          30);

      log.info("Default RSS feeds initialized");
    }
  }

  private void addDefaultFeed(
      String name, String url, String description, Integer intervalMinutes) {
    try {
      if (!rssFeedRepository.existsByUrl(url)) {
        RssFeed feed =
            RssFeed.builder()
                .name(name)
                .url(url)
                .description(description)
                .crawlIntervalMinutes(intervalMinutes)
                .isActive(true)
                .build();

        rssFeedRepository.save(feed);
        log.info("Added default RSS feed: {}", name);
      }
    } catch (Exception e) {
      log.warn("Failed to add default RSS feed: {} - {}", name, e.getMessage());
    }
  }
}
