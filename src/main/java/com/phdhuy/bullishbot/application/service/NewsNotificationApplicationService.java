package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.common.constants.CommonConstants;
import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.Subscription;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import com.phdhuy.bullishbot.domain.port.in.SendNotificationUseCase;
import com.phdhuy.bullishbot.domain.port.out.NewsNotificationRepository;
import com.phdhuy.bullishbot.domain.port.out.SubscriptionRepository;
import com.phdhuy.bullishbot.domain.port.out.TelegramService;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class NewsNotificationApplicationService implements SendNotificationUseCase {

  private final NewsNotificationRepository newsNotificationRepository;
  private final SubscriptionRepository subscriptionRepository;
  private final TelegramService telegramService;
  private final Executor taskExecutor;

  @Override
  public CompletableFuture<Void> processNewsForNotifications(NewsArticle newsArticle) {
    return CompletableFuture.runAsync(
        () -> {
          try {
            log.debug("Processing notifications for news article: {}", newsArticle.getTitle());

            Set<String> extractedTickers = newsArticle.getExtractedTickers();
            if (extractedTickers == null || extractedTickers.isEmpty()) {
              log.debug("No ticker symbols found in article: {}", newsArticle.getTitle());
              return;
            }

            Set<User> subscribedUsers = findSubscribedUsers(extractedTickers);

            log.debug(
                "Found {} users subscribed to tickers in article: {}",
                subscribedUsers.size(),
                newsArticle.getTitle());

            // Create and send notifications
            for (User user : subscribedUsers) {
              createAndSendNotification(user, newsArticle, extractedTickers);
            }

          } catch (Exception e) {
            log.error("Error processing notifications for article: {}", newsArticle.getTitle(), e);
          }
        },
        taskExecutor);
  }

  @Override
  public CompletableFuture<Void> retryFailedNotifications() {
    return CompletableFuture.runAsync(
        () -> {
          try {
            log.info("Retrying failed notifications");

            List<NewsNotification> failedNotifications =
                newsNotificationRepository.findByStatusAndRetryCountLessThan(
                    NotificationStatus.FAILED, CommonConstants.MAX_RETRY_COUNT);

            log.info("Found {} failed notifications to retry", failedNotifications.size());

            for (NewsNotification notification : failedNotifications) {
              retryNotification(notification);
            }

          } catch (Exception e) {
            log.error("Error retrying failed notifications", e);
          }
        },
        taskExecutor);
  }

  @Override
  public CompletableFuture<Void> sendNotification(
      Long telegramChatId, NewsArticle newsArticle, String matchedTickers) {
    return CompletableFuture.runAsync(
        () -> {
          try {
            String message = formatNotificationMessage(newsArticle, matchedTickers);
            telegramService.sendMessage(telegramChatId, message);

            log.debug(
                "Sent notification to user {} for article: {}",
                telegramChatId,
                newsArticle.getTitle());

          } catch (Exception e) {
            log.error(
                "Error sending notification to user {} for article: {}",
                telegramChatId,
                newsArticle.getTitle(),
                e);
            throw e;
          }
        },
        taskExecutor);
  }

  @Transactional
  protected Set<User> findSubscribedUsers(Set<String> tickerSymbols) {
    return subscriptionRepository.findByTickerSymbolIn(tickerSymbols).stream()
        .map(Subscription::getUser)
        .collect(Collectors.toSet());
  }

  @Transactional
  protected void createAndSendNotification(
      User user, NewsArticle newsArticle, Set<String> extractedTickers) {
    try {
      // Check if notification already exists
      if (newsNotificationRepository.existsByUserAndNewsArticle(user, newsArticle)) {
        log.debug(
            "Notification already exists for user {} and article: {}",
            user.getTelegramChatId(),
            newsArticle.getTitle());
        return;
      }

      // Find which tickers the user is subscribed to
      Set<String> userSubscribedTickers = getUserSubscribedTickers(user, extractedTickers);
      if (userSubscribedTickers.isEmpty()) {
        return;
      }

      // Create notification record
      NewsNotification notification =
          NewsNotification.builder()
              .user(user)
              .newsArticle(newsArticle)
              .status(NotificationStatus.PENDING)
              .build();

      notification = newsNotificationRepository.save(notification);

      // Send notification
      String matchedTickers = String.join(", ", userSubscribedTickers);
      processNotification(notification, matchedTickers);

    } catch (Exception e) {
      log.error(
          "Error creating notification for user {} and article: {}",
          user.getTelegramChatId(),
          newsArticle.getTitle(),
          e);
    }
  }

  private Set<String> getUserSubscribedTickers(User user, Set<String> extractedTickers) {
    List<Subscription> userSubscriptions = subscriptionRepository.findByUser(user);
    Set<String> userTickerSymbols =
        userSubscriptions.stream()
            .map(sub -> sub.getTicker().getSymbol())
            .collect(Collectors.toSet());

    return extractedTickers.stream()
        .filter(userTickerSymbols::contains)
        .collect(Collectors.toSet());
  }

  public void processNotification(NewsNotification notification, String matchedTickers) {
    try {
      String message = formatNotificationMessage(notification.getNewsArticle(), matchedTickers);
      telegramService.sendMessage(notification.getUser().getTelegramChatId(), message);

      updateNotificationStatus(notification.getId(), NotificationStatus.SENT, null);

      log.debug(
          "Successfully sent notification to user {} for article: {}",
          notification.getUser().getTelegramChatId(),
          notification.getNewsArticle().getTitle());

    } catch (Exception e) {
      updateNotificationStatus(notification.getId(), NotificationStatus.FAILED, e.getMessage());

      log.error(
          "Failed to send notification to user {} for article: {}",
          notification.getUser().getTelegramChatId(),
          notification.getNewsArticle().getTitle(),
          e);
    }
  }

  @Transactional
  public void updateNotificationStatus(
      UUID notificationId, NotificationStatus status, String errorMessage) {
    NewsNotification notification =
        newsNotificationRepository
            .findById(notificationId)
            .orElseThrow(
                () -> new IllegalStateException("Notification not found: " + notificationId));

    notification.setStatus(status);
    notification.setSentAt(status == NotificationStatus.SENT ? Instant.now() : null);
    notification.setErrorMessage(errorMessage);
    if (status == NotificationStatus.FAILED) {
      notification.setRetryCount(notification.getRetryCount() + 1);
    }

    newsNotificationRepository.save(notification);
  }

  @Transactional
  protected void retryNotification(NewsNotification notification) {
    try {
      Set<String> extractedTickers = notification.getNewsArticle().getExtractedTickers();
      Set<String> userSubscribedTickers =
          getUserSubscribedTickers(notification.getUser(), extractedTickers);

      if (userSubscribedTickers.isEmpty()) {
        notification.setStatus(NotificationStatus.CANCELLED);
        newsNotificationRepository.save(notification);
        return;
      }

      String matchedTickers = String.join(", ", userSubscribedTickers);
      processNotification(notification, matchedTickers);

    } catch (Exception e) {
      log.error(
          "Error retrying notification for user {} and article: {}",
          notification.getUser().getTelegramChatId(),
          notification.getNewsArticle().getTitle(),
          e);
    }
  }

  private String formatNotificationMessage(NewsArticle newsArticle, String matchedTickers) {
    StringBuilder message = new StringBuilder();

    message.append("📰 *NEWS ALERT*\n\n");

    message.append("*Ticker:* `").append(escapeMarkdown(matchedTickers)).append("`\n\n");

    message
        .append("*Title:*\n")
        .append("*")
        .append(escapeMarkdown(newsArticle.getTitle()))
        .append("*\n\n");

    if (newsArticle.getDescription() != null && !newsArticle.getDescription().isEmpty()) {
      String description = newsArticle.getDescription();
      if (description.length() > 300) {
        description = description.substring(0, 297) + "...";
      }
      message.append("*Summary:*\n").append(escapeMarkdown(description)).append("\n\n");
    }

    if (newsArticle.getPublishedDate() != null) {
      String formattedDate =
          newsArticle
              .getPublishedDate()
              .atZone(java.time.ZoneId.systemDefault())
              .format(CommonConstants.DATE_FORMATTER);
      message.append("*Published:* *").append(formattedDate).append("*\n\n");
    }

    message.append("👉 [Read Full Article](").append(newsArticle.getUrl()).append(")");

    return message.toString();
  }

  private String escapeMarkdown(String text) {
    if (text == null) {
      return "";
    }

    return text.replaceAll("([*_\\[\\]()~`>#+\\-=|{}.!])", "\\\\$1");
  }
}
