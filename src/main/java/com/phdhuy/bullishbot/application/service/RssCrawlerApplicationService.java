package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.common.constants.RssProcessingConstants;
import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.RssFeed;
import com.phdhuy.bullishbot.domain.port.in.CrawlRssFeedsUseCase;
import com.phdhuy.bullishbot.domain.port.in.ProcessNewsContentUseCase;
import com.phdhuy.bullishbot.domain.port.in.SendNotificationUseCase;
import com.phdhuy.bullishbot.domain.port.out.NewsArticleRepository;
import com.phdhuy.bullishbot.domain.port.out.RssFeedRepository;
import com.phdhuy.bullishbot.domain.service.HtmlProcessingService;
import com.phdhuy.bullishbot.domain.service.RssFeedFetcherService;
import com.phdhuy.bullishbot.domain.valueobject.FeedUrl;
import com.phdhuy.bullishbot.domain.valueobject.HtmlContent;
import com.rometools.rome.feed.synd.SyndEntry;
import com.rometools.rome.feed.synd.SyndFeed;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class RssCrawlerApplicationService implements CrawlRssFeedsUseCase {

  private final RssFeedRepository rssFeedRepository;
  private final NewsArticleRepository newsArticleRepository;
  private final ProcessNewsContentUseCase processNewsContentUseCase;
  private final SendNotificationUseCase sendNotificationUseCase;
  private final RssFeedFetcherService rssFeedFetcherService;
  private final HtmlProcessingService htmlProcessingService;
  private final Executor taskExecutor;

  @Override
  public CompletableFuture<Void> crawlAllActiveFeeds() {
    log.info("Starting to crawl all active RSS feeds");

    List<RssFeed> activeFeeds = rssFeedRepository.findByIsActiveTrue();
    log.info("Found {} active RSS feeds to crawl", activeFeeds.size());

    List<CompletableFuture<Void>> crawlTasks = activeFeeds.stream().map(this::crawlFeed).toList();

    return CompletableFuture.allOf(crawlTasks.toArray(new CompletableFuture[0]))
        .whenComplete(
            (result, throwable) -> {
              if (throwable != null) {
                log.error("Error occurred while crawling feeds", throwable);
              } else {
                log.info("Successfully completed crawling all active feeds");
              }
            });
  }

  @Override
  public CompletableFuture<Void> crawlFeed(RssFeed rssFeed) {
    return CompletableFuture.runAsync(
        () -> {
          try {
            log.info(RssProcessingConstants.LOG_CRAWLING_FEED, rssFeed.getName(), rssFeed.getUrl());

            FeedUrl feedUrl = FeedUrl.ofTrusted(rssFeed.getUrl());
            Optional<SyndFeed> syndFeedOpt = rssFeedFetcherService.fetchFeed(feedUrl.getValue());

            if (syndFeedOpt.isEmpty()) {
              log.warn(RssProcessingConstants.LOG_FEED_FAILED, rssFeed.getUrl());
              return;
            }

            processFeedEntries(rssFeed, syndFeedOpt.get().getEntries());
            updateLastCrawledTime(rssFeed);

            log.info(RssProcessingConstants.LOG_FEED_SUCCESS, rssFeed.getName());

          } catch (Exception e) {
            log.error("Error crawling RSS feed: {} ({})", rssFeed.getName(), rssFeed.getUrl(), e);
          }
        },
        taskExecutor);
  }

  @Override
  public boolean validateFeedUrl(String url) {
    try {
      FeedUrl feedUrl = FeedUrl.of(url);
      return rssFeedFetcherService.isValidFeedUrl(feedUrl.getValue());
    } catch (Exception e) {
      log.warn(RssProcessingConstants.LOG_VALIDATION_FAILED, url, e);
      return false;
    }
  }

  @Transactional
  protected void processFeedEntries(RssFeed rssFeed, List<SyndEntry> entries) {
    for (SyndEntry entry : entries) {
      try {
        if (shouldSkipEntry(entry)) {
          continue;
        }

        NewsArticle newsArticle = createNewsArticleFromEntry(rssFeed, entry);
        if (newsArticle != null) {
          newsArticle = newsArticleRepository.save(newsArticle);

          // Process content for ticker extraction
          processNewsContentUseCase.processNewsContent(newsArticle);

          // Send notifications asynchronously
          sendNotificationUseCase.processNewsForNotifications(newsArticle);
        }
      } catch (Exception e) {
        log.error("Error processing RSS entry: {}", entry.getTitle(), e);
      }
    }
  }

  private boolean shouldSkipEntry(SyndEntry entry) {
    if (entry.getUri() != null && newsArticleRepository.existsByUrl(entry.getUri())) {
      return true;
    }

    if (entry.getLink() != null && newsArticleRepository.existsByUrl(entry.getLink())) {
      return true;
    }

    return false;
  }

  private NewsArticle createNewsArticleFromEntry(RssFeed rssFeed, SyndEntry entry) {
    try {
      String url = extractUrl(entry);
      if (url == null) {
        log.warn(RssProcessingConstants.LOG_ENTRY_NO_URL, entry.getTitle());
        return null;
      }

      Instant publishedDate = extractPublishedDate(entry);
      String content = extractContent(entry);

      return NewsArticle.builder()
          .title(entry.getTitle())
          .description(cleanDescription(entry))
          .content(cleanContentHtml(content))
          .url(url)
          .author(entry.getAuthor())
          .publishedDate(publishedDate)
          .guid(entry.getUri())
          .rssFeed(rssFeed)
          .build();

    } catch (Exception e) {
      log.error(RssProcessingConstants.LOG_CREATING_ARTICLE_ERROR, entry.getTitle(), e);
      return null;
    }
  }

  private String extractUrl(SyndEntry entry) {
    return entry.getUri() != null ? entry.getUri() : entry.getLink();
  }

  private Instant extractPublishedDate(SyndEntry entry) {
    if (entry.getPublishedDate() != null) {
      return entry.getPublishedDate().toInstant();
    } else if (entry.getUpdatedDate() != null) {
      return entry.getUpdatedDate().toInstant();
    }
    return null;
  }

  private String extractContent(SyndEntry entry) {
    if (entry.getContents() != null && !entry.getContents().isEmpty()) {
      return entry.getContents().get(0).getValue();
    }

    if (entry.getDescription() != null) {
      return entry.getDescription().getValue();
    }

    return null;
  }

  private String cleanDescription(SyndEntry entry) {
    if (entry.getDescription() == null) {
      return null;
    }
    HtmlContent htmlContent = HtmlContent.of(entry.getDescription().getValue());
    return htmlProcessingService.cleanHtml(htmlContent.getValue());
  }

  private String cleanContentHtml(String content) {
    HtmlContent htmlContent = HtmlContent.of(content);
    return htmlProcessingService.cleanHtml(htmlContent.getValue());
  }

  @Transactional
  protected void updateLastCrawledTime(RssFeed rssFeed) {
    rssFeed.setLastCrawledAt(Instant.now());
    rssFeedRepository.save(rssFeed);
  }
}
