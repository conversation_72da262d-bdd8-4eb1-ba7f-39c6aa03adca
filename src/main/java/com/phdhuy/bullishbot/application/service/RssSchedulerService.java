package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.domain.port.in.CrawlRssFeedsUseCase;
import com.phdhuy.bullishbot.domain.port.in.SendNotificationUseCase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class RssSchedulerService {

  private final CrawlRssFeedsUseCase crawlRssFeedsUseCase;
  private final SendNotificationUseCase sendNotificationUseCase;
  private final RssFeedManagementService rssFeedManagementService;

  /** Crawl RSS feeds every 30 minutes */
  @Scheduled(fixedRate = 30 * 60 * 1000) // 30 minutes
  public void scheduledCrawl() {
    log.info("Starting scheduled RSS crawl");

    crawlRssFeedsUseCase
        .crawlAllActiveFeeds()
        .whenComplete(
            (result, throwable) -> {
              if (throwable != null) {
                log.error("Scheduled RSS crawl failed", throwable);
              } else {
                log.info("Scheduled RSS crawl completed successfully");
              }
            });
  }

  /** Retry failed notifications every 2 hours */
  @Scheduled(fixedRate = 2 * 60 * 60 * 1000) // 2 hours
  public void retryFailedNotifications() {
    log.info("Starting retry of failed notifications");

    sendNotificationUseCase
        .retryFailedNotifications()
        .whenComplete(
            (result, throwable) -> {
              if (throwable != null) {
                log.error("Failed notification retry failed", throwable);
              } else {
                log.info("Failed notification retry completed successfully");
              }
            });
  }

  /** Initial crawl when application starts */
  @EventListener(ApplicationReadyEvent.class)
  public void initialCrawl() {
    log.info("Application ready, initializing default RSS feeds and starting initial crawl");

    // Initialize default RSS feeds if none exist
    rssFeedManagementService.initializeDefaultFeeds();

    // Delay initial crawl by 30 seconds to allow application to fully start
    try {
      Thread.sleep(30000);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.warn("Initial crawl delay interrupted", e);
    }

    crawlRssFeedsUseCase
        .crawlAllActiveFeeds()
        .whenComplete(
            (result, throwable) -> {
              if (throwable != null) {
                log.error("Initial RSS crawl failed", throwable);
              } else {
                log.info("Initial RSS crawl completed successfully");
              }
            });
  }
}
