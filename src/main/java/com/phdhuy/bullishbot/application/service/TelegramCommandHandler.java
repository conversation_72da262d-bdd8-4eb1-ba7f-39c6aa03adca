package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.domain.port.in.SubscribeToTickerUseCase;
import com.phdhuy.bullishbot.domain.port.out.TelegramService;
import com.phdhuy.bullishbot.infrastructure.telegram.utils.TelegramMarkdownUtil;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TelegramCommandHandler {

  private final SubscribeToTickerUseCase subscribeToTickerUseCase;

  private final TelegramService telegramService;

  private static final Pattern SUBSCRIBE_PATTERN =
      Pattern.compile("^/subscribe\\s+(\\w+)$", Pattern.CASE_INSENSITIVE);
  private static final Pattern UNSUBSCRIBE_PATTERN =
      Pattern.compile("^/unsubscribe\\s+(\\w+)$", Pattern.CASE_INSENSITIVE);

  public void handleCommand(Long chatId, String command) {
    log.info("Handling command from chat {}: {}", chatId, command);

    String trimmedCommand = command.trim();

    if (trimmedCommand.equals("/start")) {
      handleStart(chatId);
    } else if (trimmedCommand.equals("/help")) {
      handleHelp(chatId);
    } else if (trimmedCommand.equals("/mytickers")) {
      handleMyTickers(chatId);
    } else {
      Matcher subscribeMatcher = SUBSCRIBE_PATTERN.matcher(trimmedCommand);
      Matcher unsubscribeMatcher = UNSUBSCRIBE_PATTERN.matcher(trimmedCommand);

      if (subscribeMatcher.matches()) {
        handleSubscribe(chatId, subscribeMatcher);
      } else if (unsubscribeMatcher.matches()) {
        handleUnsubscribe(chatId, unsubscribeMatcher);
      } else {
        handleUnknownCommand(chatId);
      }
    }
  }

  private void handleStart(Long chatId) {
    String welcomeMessage =
        """
        Welcome to Bullish Bot 📈 — Get real-time stock & crypto news alerts!

        Available commands:
        /subscribe SYMBOL - Subscribe to a ticker (e.g., /subscribe BTC, /subscribe AAPL)
        /unsubscribe SYMBOL - Remove ticker from watchlist
        /mytickers - Show your current subscriptions
        /help - Show this help message
        """;

    telegramService.sendMessageWithMenu(chatId, TelegramMarkdownUtil.escape(welcomeMessage));
  }

  private void handleHelp(Long chatId) {
    String helpMessage =
        """
        Available commands:
        /start - Welcome message and menu
        /subscribe SYMBOL - Subscribe to a ticker (e.g., /subscribe BTC, /subscribe AAPL)
        /unsubscribe SYMBOL - Remove ticker from watchlist
        /mytickers - Show your current subscriptions
        /help - Show this help message

        Examples:
        /subscribe BTC
        /subscribe AAPL
        /unsubscribe TSLA
        """;

    telegramService.sendMessage(chatId, TelegramMarkdownUtil.escape(helpMessage));
  }

  private void handleMyTickers(Long chatId) {
    List<String> subscriptions = subscribeToTickerUseCase.getUserSubscriptions(chatId);

    if (subscriptions.isEmpty()) {
      telegramService.sendMessage(
          chatId,
          TelegramMarkdownUtil.escape(
              "You have no subscriptions yet. Use /subscribe SYMBOL to add some!"));
    } else {
      String tickersList = String.join(", ", subscriptions);
      String message =
          String.format("Your subscriptions (%d): %s", subscriptions.size(), tickersList);
      telegramService.sendMessage(chatId, TelegramMarkdownUtil.escape(message));
    }
  }

  private void handleSubscribe(Long chatId, Matcher matcher) {
    String symbol = matcher.group(1).toUpperCase();
    subscribeToTickerUseCase.subscribeUserToTicker(chatId, symbol);
  }

  private void handleUnsubscribe(Long chatId, Matcher matcher) {
    String symbol = matcher.group(1).toUpperCase();
    subscribeToTickerUseCase.unsubscribeUserFromTicker(chatId, symbol);
  }

  private void handleUnknownCommand(Long chatId) {
    String message =
        """
        Unknown command. Use /help to see available commands.

        Quick commands:
        /start - Get started
        /subscribe SYMBOL - Subscribe to ticker
        /mytickers - View subscriptions
        """;

    telegramService.sendMessage(chatId, TelegramMarkdownUtil.escape(message));
  }
}
