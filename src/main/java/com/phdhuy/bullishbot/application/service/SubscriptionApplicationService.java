package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.domain.entity.Subscription;
import com.phdhuy.bullishbot.domain.entity.Ticker;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.entity.enums.TickerType;
import com.phdhuy.bullishbot.domain.port.in.SubscribeToTickerUseCase;
import com.phdhuy.bullishbot.domain.port.out.SubscriptionRepository;
import com.phdhuy.bullishbot.domain.port.out.TelegramService;
import com.phdhuy.bullishbot.domain.port.out.TickerRepository;
import com.phdhuy.bullishbot.domain.port.out.UserRepository;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SubscriptionApplicationService implements SubscribeToTickerUseCase {

  private final UserRepository userRepository;
  private final TickerRepository tickerRepository;
  private final SubscriptionRepository subscriptionRepository;
  private final TelegramService telegramService;

  @Override
  @Transactional
  public void subscribeUserToTicker(Long telegramChatId, String symbol) {
    log.info("Subscribing user {} to ticker {}", telegramChatId, symbol);

    User user = getOrCreateUser(telegramChatId);
    Ticker ticker = getOrCreateTicker(symbol.toUpperCase());

    if (subscriptionRepository.existsByUserAndTicker(user, ticker)) {
      telegramService.sendMessage(
          telegramChatId, "You are already subscribed to " + symbol.toUpperCase());
      return;
    }

    Subscription subscription = Subscription.builder().user(user).ticker(ticker).build();

    subscriptionRepository.save(subscription);
    telegramService.sendMessage(
        telegramChatId, "Successfully subscribed to " + symbol.toUpperCase());
  }

  @Override
  @Transactional
  public void unsubscribeUserFromTicker(Long telegramChatId, String symbol) {
    log.info("Unsubscribing user {} from ticker {}", telegramChatId, symbol);

    User user = userRepository.findByTelegramChatId(telegramChatId).orElse(null);
    if (user == null) {
      telegramService.sendMessage(telegramChatId, "You are not registered. Use /start first.");
      return;
    }

    Ticker ticker = tickerRepository.findBySymbol(symbol.toUpperCase()).orElse(null);
    if (ticker == null) {
      telegramService.sendMessage(telegramChatId, "Ticker " + symbol.toUpperCase() + " not found.");
      return;
    }

    Subscription subscription =
        subscriptionRepository.findByUserAndTicker(user, ticker).orElse(null);
    if (subscription == null) {
      telegramService.sendMessage(
          telegramChatId, "You are not subscribed to " + symbol.toUpperCase());
      return;
    }

    subscriptionRepository.delete(subscription);
    telegramService.sendMessage(
        telegramChatId, "Successfully unsubscribed from " + symbol.toUpperCase());
  }

  @Override
  public List<String> getUserSubscriptions(Long telegramChatId) {
    log.info("Getting subscriptions for user {}", telegramChatId);

    User user = userRepository.findByTelegramChatId(telegramChatId).orElse(null);
    if (user == null) {
      return List.of();
    }

    return subscriptionRepository.findByUser(user).stream()
        .map(subscription -> subscription.getTicker().getSymbol())
        .collect(Collectors.toList());
  }

  @Override
  public boolean isUserSubscribedToTicker(Long telegramChatId, String symbol) {
    User user = userRepository.findByTelegramChatId(telegramChatId).orElse(null);
    if (user == null) {
      return false;
    }

    Ticker ticker = tickerRepository.findBySymbol(symbol.toUpperCase()).orElse(null);
    if (ticker == null) {
      return false;
    }

    return subscriptionRepository.existsByUserAndTicker(user, ticker);
  }

  private User getOrCreateUser(Long telegramChatId) {
    return userRepository
        .findByTelegramChatId(telegramChatId)
        .orElseGet(
            () -> {
              User newUser = User.builder().telegramChatId(telegramChatId).build();
              return userRepository.save(newUser);
            });
  }

  private Ticker getOrCreateTicker(String symbol) {
    return tickerRepository
        .findBySymbol(symbol)
        .orElseGet(
            () -> {
              Ticker newTicker =
                  Ticker.builder().symbol(symbol).type(determineTickerType(symbol)).build();
              return tickerRepository.save(newTicker);
            });
  }

  private TickerType determineTickerType(String symbol) {
    if (symbol.length() <= 5 && symbol.matches("[A-Z]+")) {
      return TickerType.STOCK;
    }
    return TickerType.CRYPTO;
  }
}
