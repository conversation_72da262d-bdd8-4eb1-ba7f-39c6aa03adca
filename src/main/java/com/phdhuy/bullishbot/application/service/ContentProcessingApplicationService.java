package com.phdhuy.bullishbot.application.service;

import com.phdhuy.bullishbot.common.constants.RssProcessingConstants;
import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.port.in.ProcessNewsContentUseCase;
import com.phdhuy.bullishbot.domain.port.out.NewsArticleRepository;
import com.phdhuy.bullishbot.domain.port.out.TickerRepository;
import com.phdhuy.bullishbot.domain.service.HtmlProcessingService;
import jakarta.annotation.PostConstruct;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContentProcessingApplicationService implements ProcessNewsContentUseCase {

  private final NewsArticleRepository newsArticleRepository;
  private final TickerRepository tickerRepository;
  private final HtmlProcessingService htmlProcessingService;

  private List<String> validTickers;

  private static final Pattern TICKER_PATTERN =
      Pattern.compile(RssProcessingConstants.TICKER_PATTERN_REGEX);

  @PostConstruct
  public void init() {
    validTickers = tickerRepository.findAllSymbols();
    log.info("Loaded {} tickers into memory", validTickers.size());
  }

  @Override
  @Transactional
  public void processNewsContent(NewsArticle newsArticle) {
    Set<String> extractedTickers = extractTickerSymbols(newsArticle);
    newsArticle.setExtractedTickers(extractedTickers);
    newsArticleRepository.save(newsArticle);

    log.debug("Extracted tickers {} from article: {}", extractedTickers, newsArticle.getTitle());
  }

  @Override
  public Set<String> extractTickerSymbols(NewsArticle newsArticle) {
    StringBuilder contentBuilder = new StringBuilder();
    contentBuilder.append(newsArticle.getTitle()).append(" ");
    contentBuilder.append(cleanContent(newsArticle.getDescription())).append(" ");
    contentBuilder.append(cleanContent(newsArticle.getContent()));
    String content = contentBuilder.toString().toUpperCase();

    Set<String> tickers = new HashSet<>();
    Matcher matcher = TICKER_PATTERN.matcher(content);

    while (matcher.find()) {
      for (int i = 1; i <= RssProcessingConstants.TICKER_PATTERN_GROUPS; i++) {
        String symbol = matcher.group(i);
        if (symbol != null && validTickers.contains(symbol)) {
          tickers.add(symbol);
        }
      }
    }

    return tickers;
  }

  @Override
  public String cleanContent(String content) {
    return htmlProcessingService.cleanContent(content);
  }
}
