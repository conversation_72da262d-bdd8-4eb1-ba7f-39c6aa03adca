name: Test

on:
  push:
    branches: ["main", "feature/**"]
    tags: ["v*"]
  pull_request:
    branches: ["main"]

jobs:
  test:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: temurin
          cache: maven

      - name: Run tests
        run: mvn test

      - name: Generate code coverage report
        run: mvn jacoco:report

      - name: Archive code coverage results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: code-coverage-report
          path: target/site/jacoco
