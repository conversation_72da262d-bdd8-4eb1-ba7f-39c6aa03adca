name: Code Quality

on:
  push:
    branches: [ "main", "feature/**" ]
    tags: [ "v*" ]
  pull_request:
    branches: [ "main" ]

jobs:
  code-quality:
    name: Check coding standards
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: temurin
          cache: maven

      - name: Check formatting (Google Java Format)
        run: mvn com.spotify.fmt:fmt-maven-plugin:check

      - name: Run PMD
        run: mvn pmd:check

      - name: Run SpotBugs
        run: mvn com.github.spotbugs:spotbugs-maven-plugin:check

  test:
    name: Run tests
    needs: code-quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: temurin
          cache: maven

      - name: Run tests
        run: mvn test

      - name: Generate code coverage report
        run: mvn jacoco:report

      - name: Archive code coverage results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: code-coverage-report
          path: target/site/jacoco
